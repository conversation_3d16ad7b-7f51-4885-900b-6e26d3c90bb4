generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Admin {
  id        String    @id @default(cuid())
  email     String    @unique
  password  String
  name      String
  roleId    String?
  isActive  Boolean   @default(true)
  lastLogin DateTime?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  role      Role?     @relation(fields: [roleId], references: [id])

  @@index([email])
  @@index([isActive])
  @@index([lastLogin])
}

model User {
  id              String    @id @default(cuid())
  email           String    @unique
  name            String
  password        String
  phoneNumber     String?   @unique
  phoneVerified   Boolean   @default(false)
  phoneVerifiedAt DateTime?
  roleId          String
  isActive        Boolean   @default(true)
  lastLogin       DateTime?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  createdBy       String?
  role            Role      @relation(fields: [roleId], references: [id])

  @@index([email])
  @@index([isActive])
  @@index([lastLogin])
  @@map("users")
}

model Role {
  id          String       @id @default(cuid())
  name        String       @unique
  description String?
  isSystem    Boolean      @default(false)
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  admins      Admin[]
  users       User[]
  permissions Permission[] @relation("PermissionToRole")

  @@map("roles")
}

model Permission {
  id          String  @id @default(cuid())
  name        String  @unique
  description String?
  resource    String
  action      String
  roles       Role[]  @relation("PermissionToRole")

  @@map("permissions")
}

model Registration {
  id                                String          @id @default(cuid())
  fullName                          String
  dateOfBirth                       DateTime
  age                               Int                 @default(0)
  gender                            String
  address                           String
  branch                            String
  phoneNumber                       String
  emailAddress                      String
  emergencyContactName              String
  emergencyContactRelationship      String
  emergencyContactPhone             String
  parentGuardianName                String?
  parentGuardianPhone               String?
  parentGuardianEmail               String?
  roommateRequestConfirmationNumber String?
  medications                       String?
  allergies                         String?
  specialNeeds                      String?
  dietaryRestrictions               String?
  parentalPermissionGranted         Boolean         @default(false)
  parentalPermissionDate            DateTime?
  isVerified                        Boolean         @default(false)
  verifiedAt                        DateTime?
  verifiedBy                        String?
  qrCode                            String?
  attendanceMarked                  Boolean         @default(false)
  attendanceTime                    DateTime?
  unverifiedAt                      DateTime?
  unverifiedBy                      String?
  createdAt                         DateTime        @default(now())
  updatedAt                         DateTime        @updatedAt
  roomAllocation                    RoomAllocation?
  platoonAllocation                 String?
  platoonParticipant                PlatoonParticipant?

  @@index([isVerified])
  @@index([verifiedAt])
  @@index([emailAddress])
  @@index([phoneNumber])
  @@index([fullName])
  @@index([gender])
  @@index([branch])
  @@index([createdAt])
  @@map("registrations")
}

model Notification {
  id                String   @id @default(cuid())
  type              String
  title             String
  message           String
  priority          String   @default("medium")
  isRead            Boolean  @default(false)
  recipientId       String?
  metadata          String?
  authorizedBy      String?
  authorizedByEmail String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@map("notifications")
}

model Setting {
  id          String   @id @default(cuid())
  category    String
  key         String
  value       String
  type        String
  options     String?
  name        String
  description String?
  isSystem    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@unique([category, key])
  @@map("settings")
}

model Message {
  id             String    @id @default(cuid())
  subject        String
  content        String
  senderEmail    String
  senderName     String
  recipientEmail String
  recipientName  String
  senderType     String
  recipientType  String
  status         String    @default("sent")
  error          String?
  sentAt         DateTime  @default(now())
  deliveredAt    DateTime?
  readAt         DateTime?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  @@map("messages")
}

model Room {
  id          String           @id @default(cuid())
  name        String           @unique
  gender      String
  capacity    Int
  isActive    Boolean          @default(true)
  description String?
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  allocations RoomAllocation[]

  @@map("rooms")
}

model RoomAllocation {
  id             String       @id @default(cuid())
  registrationId String       @unique
  roomId         String
  allocatedAt    DateTime     @default(now())
  allocatedBy    String?
  room           Room         @relation(fields: [roomId], references: [id], onDelete: Cascade)
  registration   Registration @relation(fields: [registrationId], references: [id], onDelete: Cascade)

  @@map("room_allocations")
}

model SystemConfig {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("system_config")
}

model SMSVerification {
  id          String   @id @default(cuid())
  phoneNumber String
  code        String
  expiresAt   DateTime
  attempts    Int      @default(0)
  verified    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("sms_verifications")
}

model ChildrenRegistration {
  id                  String   @id @default(cuid())
  fullName            String
  dateOfBirth         DateTime
  age                 Int      @default(0)
  gender              String
  address             String
  branch              String
  parentGuardianName  String
  parentGuardianPhone String
  parentGuardianEmail String
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  @@index([fullName])
  @@index([gender])
  @@index([branch])
  @@index([createdAt])
  @@map("children_registrations")
}

model PlatoonAllocation {
  id           String   @id @default(cuid())
  name         String   @unique
  leaderName   String
  label        String   @unique
  leaderPhone  String
  capacity     Int
  createdBy    String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  participants PlatoonParticipant[]

  @@index([name])
  @@index([label])
  @@index([createdAt])
  @@map("platoon_allocations")
}

model PlatoonParticipant {
  id             String   @id @default(cuid())
  registrationId String   @unique
  platoonId      String
  allocatedBy    String?
  allocatedAt    DateTime @default(now())
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  registration Registration      @relation(fields: [registrationId], references: [id], onDelete: Cascade)
  platoon      PlatoonAllocation @relation(fields: [platoonId], references: [id], onDelete: Cascade)

  @@index([registrationId])
  @@index([platoonId])
  @@index([allocatedAt])
  @@map("platoon_participants")
}
