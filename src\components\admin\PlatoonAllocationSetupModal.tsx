'use client'

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useToast } from '@/contexts/ToastContext'
import { parseApiError } from '@/lib/error-messages'
import { X, Users } from 'lucide-react'

interface PlatoonAllocation {
  id: string
  name: string
  leaderName: string
  label: string
  leaderPhone: string
  capacity: number
  createdBy?: string
  createdAt?: string
  updatedAt?: string
  participants?: any[]
  occupancy?: number
  occupancyRate?: number
}

interface PlatoonAllocationSetupModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: () => void
  platoon?: PlatoonAllocation | null
}

export function PlatoonAllocationSetupModal({
  isOpen,
  onClose,
  onSave,
  platoon
}: PlatoonAllocationSetupModalProps) {
  const { showToast } = useToast()
  const [isLoading, setIsLoading] = useState(false)

  // Safe toast function with better error handling
  const safeShowToast = (message: string, type: 'success' | 'error') => {
    try {
      if (typeof showToast === 'function') {
        showToast(message, type)
      } else {
        console.warn('Toast function not available:', message)
      }
    } catch (error) {
      console.error('Error showing toast:', error, message)
    }
  }
  const [formData, setFormData] = useState({
    name: '',
    leaderName: '',
    label: '',
    leaderPhone: '',
    capacity: 30
  })

  useEffect(() => {
    if (platoon) {
      setFormData({
        name: platoon.name,
        leaderName: platoon.leaderName,
        label: platoon.label,
        leaderPhone: platoon.leaderPhone,
        capacity: platoon.capacity
      })
    } else {
      setFormData({
        name: '',
        leaderName: '',
        label: '',
        leaderPhone: '',
        capacity: 30
      })
    }
  }, [platoon, isOpen])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Client-side validation with toaster feedback
    if (!formData.name.trim()) {
      safeShowToast('Please enter a platoon name', 'error')
      return
    }
    if (!formData.leaderName.trim()) {
      safeShowToast('Please enter the leader name', 'error')
      return
    }
    if (!formData.label.trim()) {
      safeShowToast('Please enter a platoon label', 'error')
      return
    }
    if (!formData.leaderPhone.trim()) {
      safeShowToast('Please enter the leader phone number', 'error')
      return
    }
    if (formData.capacity < 1 || formData.capacity > 200) {
      safeShowToast('Platoon capacity must be between 1 and 200', 'error')
      return
    }

    setIsLoading(true)

    try {
      const url = platoon
        ? `/api/admin/platoon-allocations/${platoon.id}`
        : '/api/admin/platoon-allocations'

      const method = platoon ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const responseData = await response.json()

      if (!response.ok) {
        throw new Error(responseData.message || responseData.error || 'Failed to save platoon')
      }

      const successMessage = platoon
        ? `Platoon "${formData.name}" updated successfully`
        : `Platoon "${formData.name}" created successfully`

      // Show success notification
      safeShowToast(successMessage, 'success')

      // Show loading message for refresh
      safeShowToast('Updating platoon list...', 'success')

      // Trigger refresh and wait for it to complete
      await new Promise(resolve => {
        onSave()
        // Give the refresh time to complete
        setTimeout(resolve, 800)
      })

      // Close modal after refresh completes
      onClose()
    } catch (error) {
      console.error('Error saving platoon:', error)
      const errorMessage = parseApiError(error)
      safeShowToast(errorMessage, 'error')
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="bg-gradient-to-br from-indigo-500 to-purple-600 p-4 sm:p-6 border-b border-indigo-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="h-10 w-10 bg-gradient-to-br from-white/20 to-white/10 rounded-xl flex items-center justify-center shadow-lg">
                <Users className="h-5 w-5 text-white" />
              </div>
              <div>
                <h2 className="font-apercu-bold text-lg text-white">
                  {platoon ? 'Edit Platoon' : 'Create New Platoon'}
                </h2>
                <p className="font-apercu-regular text-sm text-indigo-100">
                  {platoon ? 'Update platoon details and settings' : 'Set up a new platoon for participant allocation'}
                </p>
              </div>
            </div>
            <Button
              onClick={onClose}
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 text-white hover:bg-white/10"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="p-4 sm:p-6 space-y-4">
          <div>
            <Label htmlFor="name" className="font-apercu-medium text-sm text-gray-700">
              Platoon Name *
            </Label>
            <Input
              id="name"
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="e.g., Alpha Platoon"
              required
              className="mt-1"
            />
          </div>



          <div>
            <Label htmlFor="leaderName" className="font-apercu-medium text-sm text-gray-700">
              Leader Name *
            </Label>
            <Input
              id="leaderName"
              type="text"
              value={formData.leaderName}
              onChange={(e) => setFormData(prev => ({ ...prev, leaderName: e.target.value }))}
              placeholder="e.g., John Smith"
              required
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="leaderPhone" className="font-apercu-medium text-sm text-gray-700">
              Leader Phone *
            </Label>
            <Input
              id="leaderPhone"
              type="tel"
              value={formData.leaderPhone}
              onChange={(e) => setFormData(prev => ({ ...prev, leaderPhone: e.target.value }))}
              placeholder="e.g., +1234567890"
              required
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="capacity" className="font-apercu-medium text-sm text-gray-700">
              Capacity *
            </Label>
            <Input
              id="capacity"
              type="number"
              value={formData.capacity}
              onChange={(e) => setFormData(prev => ({ ...prev, capacity: parseInt(e.target.value) || 0 }))}
              min="1"
              max="100"
              required
              className="mt-1"
            />
          </div>

          {/* Optional Label Section - Moved to Bottom */}
          <div className="border-t border-gray-200 pt-4">
            <Label htmlFor="label" className="font-apercu-medium text-sm text-gray-700">
              Platoon Label <span className="text-gray-500 font-apercu-regular">(Optional)</span>
            </Label>
            <Input
              id="label"
              type="text"
              value={formData.label}
              onChange={(e) => setFormData(prev => ({ ...prev, label: e.target.value.toUpperCase() }))}
              placeholder="e.g., ALPHA, BRAVO, CHARLIE (max 10 characters)"
              maxLength={10}
              className="mt-1"
            />
            <p className="text-xs text-gray-500 mt-1">
              {formData.label.length}/10 characters • Used for categorization and quick identification
            </p>
          </div>

          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              onClick={onClose}
              variant="outline"
              className="flex-1 font-apercu-medium"
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex-1 font-apercu-medium bg-indigo-600 hover:bg-indigo-700"
              disabled={isLoading}
            >
              {isLoading ? 'Saving...' : platoon ? 'Update Platoon' : 'Create Platoon'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
