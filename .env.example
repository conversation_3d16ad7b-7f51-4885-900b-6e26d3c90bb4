# AccoReg Environment Variables
# Copy this file to .env.local and fill in your values

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Environment (development, production, test, staging)
NODE_ENV=production

# Server port
PORT=3000

# NextAuth configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-super-secure-nextauth-secret-at-least-32-chars

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# For development (SQLite) - MUST be quoted and start with "file:"
DATABASE_URL="file:./dev.db"

# For production (PostgreSQL)
# DATABASE_URL="postgresql://username:password@localhost:5432/accoreg"

# Alternative SQLite configurations:
# DATABASE_URL="file:./database/dev.db"
# DATABASE_URL="file:/absolute/path/to/database.db"

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================

# JWT secret for token signing (minimum 32 characters)
JWT_SECRET=your-super-secure-jwt-secret-at-least-32-characters

# Security headers
SECURITY_HEADERS_ENABLED=true
CSP_ENABLED=false
HSTS_ENABLED=false

# =============================================================================
# EMAIL CONFIGURATION (Optional for development)
# =============================================================================

# SMTP settings
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_SECURE=false

# Email branding
EMAIL_FROM_NAME=AccoReg
EMAIL_REPLY_TO=<EMAIL>

# Admin email addresses (comma-separated)
ADMIN_EMAILS=<EMAIL>

# =============================================================================
# FILE UPLOAD CONFIGURATION (Optional)
# =============================================================================

# Cloudinary settings for file uploads
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# =============================================================================
# SMS CONFIGURATION (Optional)
# =============================================================================

# SMS settings
SMS_ENABLED=false
SMS_PROVIDER=mock
SMS_API_KEY=mock-api-key
SMS_API_SECRET=mock-api-secret
SMS_FROM_NUMBER=YouthReg
SMS_REGION=us-east-1

# For local SMS gateway
SMS_GATEWAY_URL=http://localhost:8080/send-sms

# =============================================================================
# REDIS CONFIGURATION (Optional)
# =============================================================================

# Redis URL for rate limiting and caching
REDIS_URL=redis://localhost:6379

# =============================================================================
# RATE LIMITING
# =============================================================================

RATE_LIMIT_ENABLED=false
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================

BACKUP_DIR=./backups
BACKUP_RETENTION_DAYS=30
BACKUP_COMPRESSION=true
BACKUP_ENCRYPTION=false
BACKUP_ENCRYPTION_KEY=your-backup-encryption-key-32-chars

# =============================================================================
# GDPR COMPLIANCE
# =============================================================================

GDPR_ENABLED=true
DATA_RETENTION_DAYS=2555
CONSENT_VERSION=1.0

# =============================================================================
# LOGGING
# =============================================================================

LOG_LEVEL=info
LOG_DIR=./logs

# =============================================================================
# MONITORING (Optional)
# =============================================================================

# Sentry for error tracking
SENTRY_DSN=https://<EMAIL>/project-id

# APM monitoring
APM_ENABLED=false
ELASTIC_APM_SERVER_URL=http://localhost:8200
ELASTIC_APM_SECRET_TOKEN=your-apm-secret-token

# =============================================================================
# SSL/TLS (Production only)
# =============================================================================

SSL_CERT_PATH=/path/to/cert.pem
SSL_KEY_PATH=/path/to/key.pem
SSL_AUTO_RENEWAL=false

# =============================================================================
# HEALTH CHECKS
# =============================================================================

HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30000

# =============================================================================
# DEVELOPMENT HELPERS
# =============================================================================

# Skip type checking during build (for faster development builds)
SKIP_TYPE_CHECK=false

# =============================================================================
# NOTES FOR DEVELOPERS
# =============================================================================

# 1. Copy this file to .env.local for development
# 2. Never commit .env.local to version control
# 3. Generate secure secrets using: node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
# 4. For Gmail SMTP, use App Passwords instead of your regular password
# 5. Most settings are optional for development - the app will work with minimal configuration
# 6. Check docs/ folder for detailed setup guides for email, SMS, etc.
