# YouthReg Production Environment Variables
# Production configuration for deployment

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Environment (MUST be production)
NODE_ENV=production

# Server port (will be overridden by hosting platform)
PORT=3000

# NextAuth configuration
NEXTAUTH_URL=https://youth-registration-system.onrender.com
NEXTAUTH_SECRET=your-super-secure-nextauth-secret-at-least-32-chars-production-change-this

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# For production (PostgreSQL) - Will be provided by Render automatically
# This will be set by Render when you add a PostgreSQL database
DATABASE_URL=postgresql://username:password@host:port/database?schema=public

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================

# JWT secret for token signing (minimum 32 characters) - CHANGE THIS
JWT_SECRET=your-super-secure-jwt-secret-at-least-32-characters-production-change-this

# Admin credentials for initial setup
SUPER_ADMIN_PASSWORD=SuperAdmin123!

# Security headers (ENABLED for production)
SECURITY_HEADERS_ENABLED=true
CSP_ENABLED=true
HSTS_ENABLED=true

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

APP_NAME=Youth Registration System
APP_URL=https://youth-registration-system.onrender.com

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================

# SMTP settings (Configure for production)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-production-app-password
SMTP_SECURE=false

# Email branding
EMAIL_FROM_NAME=YouthReg
EMAIL_REPLY_TO=<EMAIL>

# Admin email addresses (comma-separated)
ADMIN_EMAILS=<EMAIL>

# =============================================================================
# SMS CONFIGURATION
# =============================================================================

# SMS settings (Optional)
SMS_ENABLED=false
SMS_PROVIDER=mock
SMS_API_KEY=your-sms-api-key
SMS_API_SECRET=your-sms-api-secret
SMS_FROM_NUMBER=YouthReg
SMS_REGION=us-east-1

# =============================================================================
# RATE LIMITING (ENABLED for production)
# =============================================================================

RATE_LIMIT_ENABLED=true
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# =============================================================================
# GDPR COMPLIANCE
# =============================================================================

GDPR_ENABLED=true
DATA_RETENTION_DAYS=2555
CONSENT_VERSION=1.0

# =============================================================================
# LOGGING
# =============================================================================

LOG_LEVEL=warn
LOG_DIR=./logs

# =============================================================================
# REAL-TIME FEATURES
# =============================================================================

# Server-Sent Events (SSE) Configuration
SSE_HEARTBEAT_INTERVAL=15000
SSE_RECONNECT_INTERVAL=2000
SSE_CONNECTION_TIMEOUT=10000
STAFF_REALTIME_ACCESS=true
REAL_TIME_STATS=true

# Real-time update intervals
STATS_CACHE_DURATION=300000
ENABLE_REAL_TIME_UPDATES=true

# =============================================================================
# ANALYTICS & STATISTICS
# =============================================================================

ANALYTICS_ENABLED=true
ANALYTICS_RETENTION_DAYS=90

# =============================================================================
# BRANCH FEATURES
# =============================================================================

BRANCH_SELECTION_REQUIRED=true
BRANCH_VALIDATION_ENABLED=true
LEGACY_BRANCH_FALLBACK="Not Specified"

# =============================================================================
# QR CODE FEATURES
# =============================================================================

QR_CODE_ENABLED=true
ENABLE_QR_VERIFICATION=true
QR_SECRET_KEY=mopgomglobal-qr-secret-2024-production

# =============================================================================
# HEALTH CHECKS
# =============================================================================

HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30000

# =============================================================================
# PRODUCTION OPTIMIZATIONS
# =============================================================================

# Skip type checking during build (for faster production builds)
SKIP_TYPE_CHECK=true
