{"name": "mopgomglobal-admin", "version": "1.0.0", "description": "Mopgomglobal Youth Registration System - Admin Dashboard", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0 -p 3000", "build": "next build", "build:production": "cross-env NODE_ENV=production SKIP_TYPE_CHECK=true prisma generate && next build", "build:render": "npm ci && prisma generate && npx tsx scripts/deploy-database.ts && npx tsx scripts/create-super-admin.ts && npm run build", "deploy:production": "npx tsx scripts/deploy-production.ts", "test:production": "npx tsx scripts/test-production-setup.ts", "test:email": "npx tsx scripts/test-email-debug.ts", "postinstall": "prisma generate", "start": "next start", "start:production": "cross-env NODE_ENV=production next start", "lint": "next lint", "type-check": "tsc --noEmit", "setup:admin": "dotenv -e .env.local -- npx tsx scripts/create-super-admin.ts", "setup:dev": "npm run db:push && npm run db:generate && npm run setup:admin", "setup:env": "npx tsx scripts/setup-environment.ts", "setup:env:dev": "npx tsx scripts/setup-environment.ts development", "setup:env:prod": "npx tsx scripts/setup-environment.ts production", "update:permissions": "dotenv -e .env.local -- npx tsx scripts/update-permissions.ts", "create:rooms": "dotenv -e .env.local -- npx tsx scripts/create-default-rooms.ts", "create:super-admin": "dotenv -e .env.local -- npx tsx scripts/create-super-admin.ts", "setup:production": "npx tsx scripts/production-setup.ts", "check:production": "npx tsx scripts/production-checklist.ts", "seed": "npx dotenv -e .env.local -- npx tsx scripts/seed-database.ts", "seed:rooms": "npx dotenv -e .env.local -- npx tsx scripts/create-default-rooms.ts", "seed:minimal": "npx dotenv -e .env.local -- npx tsx scripts/seed-minimal.ts", "db:status": "npx dotenv -e .env.local -- npx tsx scripts/database-status.ts", "db:generate": "dotenv -e .env.local -- prisma generate", "db:push": "dotenv -e .env.local -- prisma db push", "db:push:prod": "prisma db push", "db:migrate": "dotenv -e .env.local -- prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:migrate:reset": "prisma migrate reset --force", "db:studio": "dotenv -e .env.local -- prisma studio", "db:studio:prod": "prisma studio", "clean": "rimraf .next tsconfig.tsbuildinfo", "clean:cache": "rimraf .next/cache", "fresh": "npm run clean && npm run dev"}, "dependencies": {"@prisma/client": "^6.11.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-use-is-hydrated": "^0.1.0", "@types/node": "^24.0.15", "@types/react-dom": "^18.3.7", "autoprefixer": "^10.4.21", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "ioredis": "^5.4.1", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "jsqr": "^1.4.0", "lucide-react": "^0.460.0", "next": "^15.3.3", "nodemailer": "^6.9.16", "postcss": "^8.5.1", "qrcode": "^1.5.4", "react": "^18.3.1", "react-dom": "^18.3.1", "recharts": "^3.1.0", "tailwind-merge": "^2.5.4", "tailwindcss": "^3.4.15", "zod": "^3.23.8"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/express": "^5.0.0", "@types/jsonwebtoken": "^9.0.7", "@types/nodemailer": "^6.4.15", "@types/qrcode": "^1.5.5", "@types/react": "^18.3.23", "cross-env": "^7.0.3", "dotenv-cli": "^8.0.0", "eslint": "^9.30.1", "eslint-config-next": "^15.3.3", "prettier": "^3.3.3", "prisma": "^6.11.0", "rimraf": "^6.0.1", "tsx": "^4.19.2", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "overrides": {"@types/react": "^18.3.23", "@types/react-dom": "^18.3.7"}, "keywords": ["youth", "registration", "admin", "dashboard", "nextjs", "typescript", "prisma", "tailwindcss"], "author": "Mopgomglobal Team", "license": "MIT"}