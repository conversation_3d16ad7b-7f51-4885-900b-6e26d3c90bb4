
/* Apercu Pro Font Family - Compulsory Loading */
/* Using font-display: block to ensure fonts are loaded before text is shown */

@font-face {
  font-family: 'Apercu Pro';
  src: url('/fonts/ApercuPro-Regular.woff') format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: block; /* Changed from swap to block for compulsory loading */
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Apercu Pro';
  src: url('/fonts/ApercuPro-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: block; /* Changed from swap to block for compulsory loading */
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Apercu Pro';
  src: url('/fonts/ApercuPro-Bold.woff') format('woff');
  font-weight: 700;
  font-style: normal;
  font-display: block; /* Changed from swap to block for compulsory loading */
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* Font stack - Apercu Pro with fallbacks */
.font-apercu {
  font-family: 'Apercu Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.font-apercu-regular {
  font-family: 'Apercu Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-weight: 400;
}

.font-apercu-medium {
  font-family: 'Apercu Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-weight: 500;
}

.font-apercu-bold {
  font-family: 'Apercu Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-weight: 700;
}

/* Enhanced text rendering */
.text-enhanced {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: 'kern' 1, 'liga' 1;
}
