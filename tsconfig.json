{"compilerOptions": {"target": "ES2022", "lib": ["dom", "dom.iterable", "es6", "ES2022"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/contexts/*": ["./src/contexts/*"], "@/hooks/*": ["./src/hooks/*"], "@/lib/*": ["./src/lib/*"], "@/styles/*": ["./src/styles/*"], "@/types/*": ["./src/types/*"], "@/app/*": ["./src/app/*"]}, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": false, "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": false, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "noImplicitOverride": true, "exactOptionalPropertyTypes": false, "noPropertyAccessFromIndexSignature": false, "allowUnusedLabels": false, "allowUnreachableCode": false}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/**/*", "prisma/**/*", "scripts/**/*"], "exclude": ["node_modules", ".next", "dist", "build", "coverage", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}