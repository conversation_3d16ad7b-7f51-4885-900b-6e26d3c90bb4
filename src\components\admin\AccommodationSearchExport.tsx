'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card } from '@/components/ui/card'
import { useToast } from '@/contexts/ToastContext'
import { parseApiError } from '@/lib/error-messages'
import {
  Search,
  Download,
  FileText,
  Users,
  Home,
  Eye,
  Loader2,
  Filter,
  X
} from 'lucide-react'
import { calculateAge } from '@/lib/age-calculator'
import { capitalizeName } from '@/lib/utils'

interface SearchResult {
  id: string
  fullName: string
  gender: string
  dateOfBirth: string
  phoneNumber: string
  emailAddress: string
  roomAllocation?: {
    id: string
    room: {
      id: string
      name: string
      gender: string
      capacity: number
    }
  }
}

interface AccommodationSearchExportProps {
  onPersonSelectAction?: (registrationId: string) => void
  refreshTrigger?: number
  canExport?: boolean
  canViewPersonDetails?: boolean
  isViewerOnly?: boolean
}

export function AccommodationSearchExport({
  onPersonSelectAction,
  refreshTrigger,
  canExport = true,
  canViewPersonDetails = true,
  isViewerOnly = false
}: AccommodationSearchExportProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<SearchResult[]>([])
  const [loading, setLoading] = useState(false)
  const [exporting, setExporting] = useState<string | null>(null)
  const [filterType] = useState<'all' | 'allocated' | 'unallocated'>('all') // Always search all
  const [showResults, setShowResults] = useState(false)

  const { success, error } = useToast()

  const showToast = (title: string, type: 'success' | 'error' | 'warning' | 'info') => {
    if (type === 'success') {
      success(title)
    } else if (type === 'error') {
      error(title)
    }
  }

  const searchRegistrants = async () => {
    if (!searchQuery.trim()) {
      setSearchResults([])
      setShowResults(false)
      return
    }

    try {
      setLoading(true)

      const params = new URLSearchParams({
        search: searchQuery.trim(),
        filter: filterType
      })

      const response = await fetch(`/api/admin/accommodations/search?${params}`)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to search registrants')
      }

      const data = await response.json()
      setSearchResults(data.results || [])
      setShowResults(true)
    } catch (error) {
      console.error('Error searching registrants:', error)
      const errorMessage = parseApiError(error)
      showToast(errorMessage.description, 'error')
    } finally {
      setLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      searchRegistrants()
    }
  }

  const clearSearch = () => {
    setSearchQuery('')
    setSearchResults([])
    setShowResults(false)
  }

  const exportData = async (format: 'csv' | 'pdf') => {
    try {
      setExporting(format)

      const params = new URLSearchParams({
        format,
        ...(searchQuery.trim() && { search: searchQuery.trim() }),
        filter: filterType
      })

      const response = await fetch(`/api/admin/accommodations/export?${params}`)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Failed to export ${format.toUpperCase()}`)
      }

      if (format === 'pdf') {
        // For PDF, the server returns HTML content that opens in a new window for printing
        const htmlContent = await response.text()
        const printWindow = window.open('', '_blank', 'width=800,height=600')

        if (printWindow) {
          printWindow.document.write(htmlContent)
          printWindow.document.close()
          showToast('PDF Export Successful - PDF report opened in a new window. Use your browser\'s print function to save as PDF.', 'success')
        } else {
          // Fallback: download as HTML file
          const blob = new Blob([htmlContent], { type: 'text/html' })
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.style.display = 'none'
          a.href = url
          a.download = `accommodation-report-${new Date().toISOString().split('T')[0]}.html`
          document.body.appendChild(a)
          a.click()
          window.URL.revokeObjectURL(url)
          document.body.removeChild(a)
          showToast('PDF export downloaded as HTML file. Open in browser and print to save as PDF.', 'success')
        }
      } else {
        // For CSV, download the file directly
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = url
        a.download = `accommodation-report-${new Date().toISOString().split('T')[0]}.${format}`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        showToast(`${format.toUpperCase()} exported successfully`, 'success')
      }
    } catch (error) {
      console.error(`Error exporting ${format}:`, error)
      const errorMessage = parseApiError(error)
      showToast(errorMessage.description, 'error')
    } finally {
      setExporting(null)
    }
  }

  const calculateAge = (dateOfBirth: string): number => {
    const today = new Date()
    const birthDate = new Date(dateOfBirth)
    let age = today.getFullYear() - birthDate.getFullYear()
    const monthDiff = today.getMonth() - birthDate.getMonth()
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--
    }
    return age
  }

  // Refresh search results when refreshTrigger changes
  useEffect(() => {
    if (refreshTrigger && showResults) {
      searchRegistrants()
    }
  }, [refreshTrigger])

  // Real-time search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchQuery.trim()) {
        searchRegistrants()
      } else {
        setSearchResults([])
        setShowResults(false)
      }
    }, 300) // Debounce search by 300ms

    return () => clearTimeout(timeoutId)
  }, [searchQuery]) // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <div className="space-y-6 bg-white">
      {/* Search and Filter Section */}
      <Card className="p-4 sm:p-6">
        <div className="space-y-4">
          {/* Search Input */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Search by name, email, phone number..."
              className="pl-10 pr-10 font-apercu-regular"
            />
            {searchQuery && (
              <button
                onClick={clearSearch}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>

          {/* Real-time search info */}
          <div className="text-sm text-gray-500 font-apercu-regular">
            Start typing to search participants
          </div>

          {/* Export Buttons */}
          {canExport && !isViewerOnly && (
            <div className="flex flex-col sm:flex-row gap-2 pt-4 border-t border-gray-200">
              <Button
                variant="outline"
                size="sm"
                onClick={() => exportData('csv')}
                disabled={!!exporting}
                className="font-apercu-medium w-full sm:w-auto"
              >
                {exporting === 'csv' ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Download className="h-4 w-4 mr-2" />
                )}
                Export CSV
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => exportData('pdf')}
                disabled={!!exporting}
                className="font-apercu-medium w-full sm:w-auto"
              >
                {exporting === 'pdf' ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <FileText className="h-4 w-4 mr-2" />
                )}
                Export PDF
              </Button>
            </div>
          )}
        </div>
      </Card>

      {/* Search Results */}
      {showResults && (
        <Card className="p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 gap-2">
            <h3 className="font-apercu-bold text-lg text-gray-900">
              Search Results ({searchResults.length})
            </h3>
            {searchQuery && (
              <Badge variant="outline" className="font-apercu-medium w-fit">
                "{searchQuery}" • {filterType}
              </Badge>
            )}
          </div>

          {searchResults.length === 0 ? (
            <div className="text-center py-8">
              <Search className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <p className="font-apercu-medium text-gray-500">No registrants found</p>
              <p className="font-apercu-regular text-sm text-gray-400">
                Try adjusting your search terms or filters
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-1.5 sm:gap-3 md:gap-4 max-h-96 overflow-y-auto">
              {searchResults.map((person) => (
                <div
                  key={person.id}
                  className="p-1.5 sm:p-3 md:p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-all duration-200 hover:shadow-sm cursor-pointer"
                >
                  <div className="flex flex-col space-y-1 sm:space-y-2">
                    <div className="flex items-center space-x-1 sm:space-x-2">
                      <div className={`h-3 w-3 sm:h-5 sm:w-5 md:h-6 md:w-6 rounded-full flex items-center justify-center flex-shrink-0 ${
                        person.gender === 'Male'
                          ? 'bg-gradient-to-r from-blue-500 to-blue-600'
                          : 'bg-gradient-to-r from-pink-500 to-pink-600'
                      }`}>
                        <Users className="h-1.5 w-1.5 sm:h-2.5 sm:w-2.5 md:h-3 md:w-3 text-white" />
                      </div>
                      <span className="font-apercu-medium text-xs sm:text-sm text-gray-900 truncate leading-tight">
                        {capitalizeName(person.fullName)}
                      </span>
                    </div>
                    <div className="flex items-center justify-between gap-1">
                      <span className="font-apercu-regular text-xs text-gray-500 truncate">
                        {calculateAge(person.dateOfBirth)} yrs
                      </span>
                      {canViewPersonDetails && onPersonSelectAction && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            onPersonSelectAction(person.id)
                          }}
                          className="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6 rounded-full bg-indigo-100 hover:bg-indigo-200 flex items-center justify-center transition-colors flex-shrink-0"
                          title="View details"
                        >
                          <Eye className="h-2 w-2 sm:h-2.5 sm:w-2.5 md:h-3 md:w-3 text-indigo-600" />
                        </button>
                      )}
                    </div>
                    <div className="flex items-center justify-center">
                      {person.roomAllocation ? (
                        <Badge className="bg-green-100 text-green-800 border-0 text-xs px-1 py-0.5 truncate w-full text-center">
                          {person.roomAllocation.room.name}
                        </Badge>
                      ) : (
                        <Badge variant="secondary" className="text-xs px-1 py-0.5 w-full text-center">
                          Unallocated
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </Card>
      )}
    </div>
  )
}
