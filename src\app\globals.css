@tailwind base;
@tailwind components;
@tailwind utilities;

@import "../styles/fonts.css";
@import "../styles/animations.css";

/* Tailwind CSS custom variant - suppress CSS linter warning */
/* stylelint-disable-next-line at-rule-no-unknown */
@custom-variant dark (&:is(.dark *));

/* Enhanced text rendering */
.text-enhanced {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: 'kern' 1, 'liga' 1;
}

/* Mobile touch handling improvements */
.touch-manipulation {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.select-none {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Remove underlines from all links by default */
a {
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

/* Navigation specific styles */
.no-underline {
  text-decoration: none !important;
}

.hover\:no-underline:hover {
  text-decoration: none !important;
}

/* Line clamp utilities for text truncation */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  line-clamp: 3;
}

/* Text capitalization utilities */
.text-capitalize {
  text-transform: capitalize !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

.text-lowercase {
  text-transform: lowercase !important;
}

/* Text capitalization utilities available but not auto-applied */

:root {
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}



/* Tailwind CSS theme configuration - suppress CSS linter warning */
/* stylelint-disable-next-line at-rule-no-unknown */
@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --font-sans: 'Apercu Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-destructive: var(--destructive);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

/* Enhanced typography and visibility - Compulsory Apercu Pro */
html {
  font-family: 'Apercu Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
}

body {
  font-family: 'Apercu Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  line-height: 1.6;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: 'kern' 1, 'liga' 1;
}

/* Force Apercu Pro on all elements */
* {
  font-family: 'Apercu Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
}

/* Improved text contrast and readability with Apercu Pro weights */
h1, h2, h3, h4, h5, h6 {
  color: #111827;
  font-family: 'Apercu Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-weight: 700; /* Apercu Pro Bold */
  line-height: 1.3;
  letter-spacing: -0.025em;
}

p, span, div {
  color: #374151;
  font-family: 'Apercu Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-weight: 400; /* Apercu Pro Regular */
}

/* Form elements with better visibility and Apercu font */
input, textarea, select {
  color: #111827 !important;
  background-color: #ffffff !important;
  border-color: #d1d5db !important;
  font-family: 'Apercu Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  font-weight: 400 !important;
}

input::placeholder, textarea::placeholder {
  color: #9ca3af !important;
  font-family: 'Apercu Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
}

/* Button text visibility */
button {
  font-weight: 500;
}

/* Link visibility */
a {
  color: #4f46e5;
  text-decoration: none;
}

a:hover {
  color: #3730a3;
  text-decoration: underline;
}

/* Error text visibility */
.text-red-600, .text-red-700 {
  color: #dc2626 !important;
  font-weight: 500;
}

/* Success text visibility */
.text-green-600, .text-green-700 {
  color: #059669 !important;
  font-weight: 500;
}

/* Warning text visibility */
.text-yellow-600, .text-yellow-700 {
  color: #d97706 !important;
  font-weight: 500;
}

/* Text visibility classes are now handled by Apercu Pro utilities above */

/* Enhanced visibility for specific UI elements */
.text-white {
  color: #ffffff !important;
  font-weight: 500;
}

.text-indigo-600 {
  color: #4f46e5 !important;
  font-weight: 500;
}

.text-indigo-700 {
  color: #4338ca !important;
  font-weight: 500;
}

/* Button text enhancement with Apercu Pro */
button, .btn {
  font-family: 'Apercu Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-weight: 500 !important; /* Apercu Pro Medium */
  letter-spacing: 0.025em;
}

/* Form label enhancement with Apercu Pro */
label {
  color: #374151 !important;
  font-family: 'Apercu Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-weight: 500 !important; /* Apercu Pro Medium */
}

/* Navigation and header text */
nav, header {
  font-weight: 500;
}

/* Ensure proper contrast for all interactive elements */
a, button, [role="button"] {
  color: inherit;
  font-weight: 500;
}

a:hover, button:hover, [role="button"]:hover {
  color: inherit;
  opacity: 0.8;
}

/* Font weight utilities are defined in fonts.css */

/* Apply Apercu Pro weights to specific text classes */
.text-gray-900 {
  color: #111827 !important;
  font-family: 'Apercu Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-weight: 500; /* Medium for emphasis */
}

.text-gray-800 {
  color: #1f2937 !important;
  font-family: 'Apercu Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-weight: 500; /* Medium for emphasis */
}

.text-gray-700 {
  color: #374151 !important;
  font-family: 'Apercu Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-weight: 400; /* Regular */
}

.text-gray-600 {
  color: #4b5563 !important;
  font-family: 'Apercu Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-weight: 400; /* Regular */
}

.text-gray-500 {
  color: #6b7280 !important;
  font-family: 'Apercu Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-weight: 400; /* Regular */
}

/* Light Mode Only - Consistent Experience */
html {
  color-scheme: light only;
}

html.light,
body {
  background-color: #ffffff !important;
  color: #111827 !important;
}

/* Ensure all elements use light mode colors */
* {
  color-scheme: light only;
}

/* Tailwind CSS base layer with apply directives */
/* stylelint-disable at-rule-no-unknown */
@layer base {
  * {
    @apply border-gray-200;
  }
  body {
    @apply bg-white text-gray-900 font-apercu antialiased;
  }
}
/* stylelint-enable at-rule-no-unknown */

/* Enhanced Color System - Red backgrounds with white text */
.bg-red-600, .bg-red-700, .bg-red-500 {
  color: #ffffff !important;
}

.bg-destructive {
  color: #ffffff !important;
}

/* Enhanced Color System - Colored backgrounds with white text */
.bg-indigo-600, .bg-indigo-700, .bg-indigo-500 {
  color: #ffffff !important;
}

.bg-green-600, .bg-green-700, .bg-green-500 {
  color: #ffffff !important;
}

.bg-blue-600, .bg-blue-700, .bg-blue-500 {
  color: #ffffff !important;
}

.bg-amber-600, .bg-amber-700, .bg-amber-500 {
  color: #ffffff !important;
}

.bg-purple-600, .bg-purple-700, .bg-purple-500 {
  color: #ffffff !important;
}

/* Badge specific color overrides */
[data-slot="badge"] {
  font-weight: 500 !important;
}

/* Button specific color overrides */
.bg-indigo-600 {
  background-color: #4f46e5 !important;
  color: #ffffff !important;
}

.bg-red-600 {
  background-color: #dc2626 !important;
  color: #ffffff !important;
}

.bg-green-600 {
  background-color: #16a34a !important;
  color: #ffffff !important;
}

.bg-amber-600 {
  background-color: #d97706 !important;
  color: #ffffff !important;
}

.bg-blue-600 {
  background-color: #2563eb !important;
  color: #ffffff !important;
}
