# Render Production Deployment Guide

Complete guide for deploying Mopgomyouth to Render with PostgreSQL database.

## 🚀 Pre-Deployment Checklist

### ✅ Code Preparation
- [x] Database provider changed to PostgreSQL
- [x] Production environment variables configured
- [x] Render.yaml optimized for PostgreSQL
- [x] Build scripts updated for production
- [x] Test/debug files removed
- [x] Real-time functionality configured

## 📋 Step-by-Step Deployment

### 1. Create Render Account & Services

#### A. Create Web Service
1. Go to [Render Dashboard](https://dashboard.render.com)
2. Click "New +" → "Web Service"
3. Connect your GitHub repository
4. Configure service:
   - **Name**: `mopgomyouth`
   - **Environment**: `Node`
   - **Region**: `Oregon` (or your preferred region)
   - **Branch**: `main`
   - **Build Command**: (will use render.yaml)
   - **Start Command**: `npm start`

#### B. Create PostgreSQL Database
1. In Render Dashboard, click "New +" → "PostgreSQL"
2. Configure database:
   - **Name**: `mopgomyouth-db`
   - **Database**: `mopgomyouth`
   - **User**: `mopgomyouth_user`
   - **Region**: Same as web service
   - **Plan**: Free (or paid as needed)

### 2. Configure Environment Variables

In your Render web service, go to **Environment** tab and add:

#### Required Variables
```bash
# Database (Auto-generated by Render when you link PostgreSQL)
DATABASE_URL=postgresql://username:password@host:port/database

# Authentication & Security
NODE_ENV=production
NEXTAUTH_SECRET=your-super-secure-nextauth-secret-at-least-32-chars
JWT_SECRET=your-super-secure-jwt-secret-at-least-32-characters
NEXTAUTH_URL=https://your-app-name.onrender.com

# Admin Setup
SUPER_ADMIN_PASSWORD=SuperAdmin123!
DEFAULT_ADMIN_EMAIL=<EMAIL>
DEFAULT_ADMIN_PASSWORD=Admin123!

# Application Settings
APP_NAME=Mopgomyouth
SYSTEM_NAME=Mopgomyouth
ORGANIZATION_NAME=Mopgomglobal
SUPPORT_EMAIL=<EMAIL>

# Real-time Features
SSE_HEARTBEAT_INTERVAL=15000
SSE_RECONNECT_INTERVAL=2000
SSE_CONNECTION_TIMEOUT=10000
STAFF_REALTIME_ACCESS=true
REAL_TIME_STATS=true
ENABLE_REAL_TIME_UPDATES=true

# Analytics & Statistics
ANALYTICS_ENABLED=true
STATS_CACHE_DURATION=300000
ANALYTICS_RETENTION_DAYS=90

# Security (Production)
SECURITY_HEADERS_ENABLED=true
CSP_ENABLED=true
HSTS_ENABLED=true
RATE_LIMIT_ENABLED=true

# Features
QR_CODE_ENABLED=true
ENABLE_QR_VERIFICATION=true
QR_SECRET_KEY=mopgomglobal-qr-secret-2024-production
REGISTRATION_OPEN=true
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_ACCOMMODATIONS=true
ENABLE_ROOM_ALLOCATION=true
ENABLE_BULK_OPERATIONS=true
ENABLE_ANALYTICS=true
ENABLE_REPORTS=true
ENABLE_NOTIFICATIONS=true
ENABLE_USER_MANAGEMENT=true
ENABLE_ROLE_MANAGEMENT=true
ENABLE_SETTINGS_MANAGEMENT=true
ENABLE_AUDIT_LOGS=true

# Branch Features
BRANCH_SELECTION_REQUIRED=true
BRANCH_VALIDATION_ENABLED=true
LEGACY_BRANCH_FALLBACK="Not Specified"

# Age Settings
MINIMUM_AGE=13

# Permissions & Access
AUTO_VERIFY_REGISTRATIONS=false
ALLOW_DUPLICATE_REGISTRATIONS=false
ENABLE_PARENTAL_PERMISSION=true
ENABLE_EMERGENCY_CONTACTS=true
ENABLE_MEDICAL_INFO=true
ENABLE_DIETARY_RESTRICTIONS=true
ENABLE_SPECIAL_NEEDS=true

# Health Checks
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30000

# Logging
LOG_LEVEL=warn
```

#### Email Configuration (Optional)
```bash
# SMTP Settings
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_SECURE=false
EMAIL_FROM_NAME=Mopgomyouth
EMAIL_REPLY_TO=<EMAIL>
ADMIN_EMAILS=<EMAIL>
EMAIL_NOTIFICATIONS_ENABLED=true
SEND_REGISTRATION_EMAILS=true
SEND_ADMIN_NOTIFICATIONS=true
```

### 3. Link Database to Web Service

1. In your web service settings, go to **Environment**
2. The `DATABASE_URL` should automatically appear when you link the PostgreSQL service
3. If not, manually copy the connection string from your PostgreSQL service

### 4. Deploy Application

1. **Automatic Deployment**: Push to your main branch
2. **Manual Deployment**: Click "Deploy latest commit" in Render dashboard

### 5. Monitor Deployment

Watch the build logs for:
- ✅ Dependencies installation
- ✅ Prisma client generation
- ✅ Database migrations
- ✅ Super admin creation
- ✅ Settings seeding
- ✅ Application build
- ✅ Service start

## 🔍 Post-Deployment Verification

### 1. Check Application Health
```bash
curl https://your-app-name.onrender.com/api/health
```

### 2. Verify Database Connection
```bash
# Run production setup check
npm run setup:production
```

### 3. Test Admin Login
1. Go to `https://your-app-name.onrender.com/admin/login`
2. Login with:
   - **Email**: `<EMAIL>`
   - **Password**: `SuperAdmin123!`

### 4. Test Real-time Features
1. Open attendance page
2. Check browser console for SSE connection
3. Verify real-time updates work

## 🐛 Troubleshooting

### Build Failures
```bash
# Check logs for:
- Missing environment variables
- Database connection issues
- Migration failures
- TypeScript errors
```

### Database Issues
```bash
# Reset migrations if needed
npm run db:migrate:reset
npm run db:migrate:deploy
```

### Real-time Not Working
```bash
# Verify environment variables:
- SSE_HEARTBEAT_INTERVAL=15000
- SSE_RECONNECT_INTERVAL=2000
- STAFF_REALTIME_ACCESS=true
- REAL_TIME_STATS=true
```

## 📊 Performance Optimization

### 1. Database Optimization
- Use connection pooling (built into Render PostgreSQL)
- Monitor query performance
- Add indexes for frequently queried fields

### 2. Application Optimization
- Enable compression (already configured)
- Use CDN for static assets
- Monitor memory usage

### 3. Real-time Optimization
- Monitor SSE connection count
- Adjust heartbeat intervals if needed
- Use Redis for scaling (upgrade plan)

## 🔒 Security Checklist

- [x] HTTPS enforced (automatic on Render)
- [x] Security headers enabled
- [x] Rate limiting enabled
- [x] Environment variables secured
- [x] Database access restricted
- [x] Admin passwords strong
- [x] CORS properly configured

## 📈 Monitoring & Maintenance

### 1. Regular Checks
- Monitor application logs
- Check database performance
- Verify backup status
- Monitor real-time connections

### 2. Updates
- Keep dependencies updated
- Monitor security advisories
- Test updates in staging first

### 3. Scaling
- Monitor resource usage
- Upgrade plan as needed
- Consider Redis for session storage

## 🎉 Success Indicators

Your deployment is successful when:
- ✅ Application loads without errors
- ✅ Admin login works
- ✅ Database operations function
- ✅ Real-time features work
- ✅ Registration process works
- ✅ Room allocation functions
- ✅ QR code scanning works
- ✅ Email notifications send (if configured)

## 📞 Support

If you encounter issues:
1. Check Render service logs
2. Verify environment variables
3. Test database connectivity
4. Check browser console for errors
5. Monitor real-time connection status

Your Mopgomyouth application is now ready for production on Render with PostgreSQL!
