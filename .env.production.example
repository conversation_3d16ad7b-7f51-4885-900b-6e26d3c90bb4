# Production Environment Variables Template
# Copy this file to .env.production and fill in your actual values

# Core Application
NODE_ENV=production
DATABASE_URL=postgresql://username:password@host:port/database
NEXTAUTH_SECRET=your-nextauth-secret-here
NEXTAUTH_URL=https://your-domain.com
JWT_SECRET=your-jwt-secret-here
ENCRYPTION_KEY=your-encryption-key-here

# Super Admin Setup
SUPER_ADMIN_PASSWORD=your-secure-password-here

# Performance & Caching
REDIS_URL=redis://localhost:6379
RATE_LIMIT_ENABLED=true
LOG_LEVEL=warn
HEALTH_CHECK_ENABLED=true

# Build Optimizations
SKIP_TYPE_CHECK=false
NEXT_TELEMETRY_DISABLED=1
ANALYZE=false

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_SECURE=false
EMAIL_FROM_NAME=YourAppName
EMAIL_REPLY_TO=<EMAIL>
ADMIN_EMAILS=<EMAIL>
EMAIL_NOTIFICATIONS_ENABLED=true
SEND_REGISTRATION_EMAILS=true
SEND_ADMIN_NOTIFICATIONS=true

# Security & Headers
SECURITY_HEADERS_ENABLED=true
CSP_ENABLED=true
HSTS_ENABLED=true
BACKUP_ENCRYPTION=true
BACKUP_ENCRYPTION_KEY=your-backup-encryption-key

# GDPR & Compliance
GDPR_ENABLED=true
DATA_RETENTION_DAYS=2555
CONSENT_VERSION=1.0

# Production Performance
MAX_REQUEST_SIZE=10mb
BODY_PARSER_LIMIT=10mb
CONNECTION_POOL_SIZE=10
QUERY_TIMEOUT=30000

# Monitoring & Analytics
ENABLE_ANALYTICS=true
ENABLE_PERFORMANCE_MONITORING=true
ERROR_REPORTING_ENABLED=true
