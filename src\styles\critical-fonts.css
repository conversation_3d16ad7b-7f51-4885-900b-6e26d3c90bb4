/* Critical Apercu Pro Font Loading - Inline CSS for immediate loading */
/* This CSS should be inlined in the HTML head for fastest loading */

@font-face {
  font-family: 'Apercu Pro';
  src: url('/fonts/ApercuPro-Regular.woff') format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: block;
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Apercu Pro';
  src: url('/fonts/ApercuPro-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: block;
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Apercu Pro';
  src: url('/fonts/ApercuPro-Bold.woff') format('woff');
  font-weight: 700;
  font-style: normal;
  font-display: block;
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* Immediate font application */
html, body, * {
  font-family: 'Apercu Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
}

/* Font loading optimization */
.font-loading {
  visibility: hidden;
}

.fonts-loaded {
  visibility: visible;
}

/* Prevent FOIT (Flash of Invisible Text) */
.apercu-text {
  font-family: 'Apercu Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-display: block;
}
